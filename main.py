#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MaaFramework自动化任务系统 - 主启动文件
功能：整合所有模块，提供统一的启动入口
作者：AI Assistant
日期：2025-08-05
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox, ttk
import logging

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from maa_automation_system import MaaResourceManager, MaaTaskExecutor
    from maa_custom_actions import MaaCustomActionRegistry, MaaEnhancedTaskExecutor
    from maa_ui import MaaAutomationUI
    from maa_ui_design import MaaDesignUI
except ImportError as e:
    print(f"导入模块失败: {e}")
    sys.exit(1)

class MaaLauncher:
    """MaaFramework自动化任务系统启动器"""
    
    def __init__(self):
        """初始化启动器"""
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
    def setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('maa_launcher.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    
    def show_launcher_ui(self):
        """显示启动器界面"""
        root = tk.Tk()
        root.title("MaaFramework自动化任务系统 - 启动器")
        root.geometry("500x350")
        root.resizable(False, False)
        
        # 设置窗口居中
        root.update_idletasks()
        x = (root.winfo_screenwidth() // 2) - (500 // 2)
        y = (root.winfo_screenheight() // 2) - (350 // 2)
        root.geometry(f"500x350+{x}+{y}")
        
        # 主框架
        main_frame = ttk.Frame(root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(
            main_frame, 
            text="MaaFramework自动化任务系统",
            font=('Microsoft YaHei', 16, 'bold')
        )
        title_label.pack(pady=(0, 20))
        
        # 描述
        desc_label = ttk.Label(
            main_frame,
            text="选择要启动的界面模式：",
            font=('Microsoft YaHei', 12)
        )
        desc_label.pack(pady=(0, 20))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 完整功能界面按钮
        full_ui_button = ttk.Button(
            button_frame,
            text="完整功能界面",
            command=lambda: self.launch_ui('full', root),
            width=20
        )
        full_ui_button.pack(pady=5)
        
        # 简洁设计界面按钮
        design_ui_button = ttk.Button(
            button_frame,
            text="简洁设计界面",
            command=lambda: self.launch_ui('design', root),
            width=20
        )
        design_ui_button.pack(pady=5)
        
        # 命令行模式按钮
        cli_button = ttk.Button(
            button_frame,
            text="命令行模式",
            command=lambda: self.launch_cli(root),
            width=20
        )
        cli_button.pack(pady=5)
        
        # 测试配置按钮
        test_button = ttk.Button(
            button_frame,
            text="测试配置",
            command=lambda: self.test_configuration(root),
            width=20
        )
        test_button.pack(pady=5)
        
        # 信息框架
        info_frame = ttk.LabelFrame(main_frame, text="系统信息", padding="10")
        info_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 系统信息
        info_text = tk.Text(info_frame, height=6, width=50, wrap=tk.WORD)
        info_text.pack(fill=tk.BOTH, expand=True)
        
        # 添加系统信息
        info_content = self.get_system_info()
        info_text.insert(tk.END, info_content)
        info_text.config(state=tk.DISABLED)
        
        root.mainloop()
    
    def get_system_info(self):
        """获取系统信息"""
        try:
            # 检查资源文件
            resource_manager = MaaResourceManager()
            pipeline_exists = resource_manager.pipeline_path.exists()
            email_file_exists = (resource_manager.resource_path / "pipeline" / "邮箱.txt").exists()
            
            info = f"""资源配置状态：
• Pipeline配置: {'✓ 存在' if pipeline_exists else '✗ 不存在'}
• 邮箱文件: {'✓ 存在' if email_file_exists else '✗ 不存在'}
• 任务数量: {len(resource_manager.pipeline_config)}
• 自定义动作: 已注册 5 个

系统版本: v1.0.0
开发者: AI Assistant"""
            
            return info
            
        except Exception as e:
            return f"获取系统信息失败: {e}"
    
    def launch_ui(self, ui_type, parent_root):
        """启动UI界面"""
        try:
            parent_root.destroy()
            
            if ui_type == 'full':
                self.logger.info("启动完整功能界面")
                app = MaaAutomationUI()
            elif ui_type == 'design':
                self.logger.info("启动简洁设计界面")
                app = MaaDesignUI()
            else:
                raise ValueError(f"未知的UI类型: {ui_type}")
            
            app.run()
            
        except Exception as e:
            self.logger.error(f"启动UI失败: {e}")
            messagebox.showerror("错误", f"启动UI失败: {e}")
    
    def launch_cli(self, parent_root):
        """启动命令行模式"""
        try:
            parent_root.destroy()
            self.logger.info("启动命令行模式")
            
            print("\n" + "="*50)
            print("MaaFramework自动化任务系统 - 命令行模式")
            print("="*50)
            
            # 创建资源管理器
            resource_manager = MaaResourceManager()
            custom_actions = MaaCustomActionRegistry()
            executor = MaaEnhancedTaskExecutor(resource_manager, custom_actions)
            
            # 显示任务序列
            sequence = resource_manager.get_task_sequence()
            print(f"\n任务序列 (共{len(sequence)}个任务):")
            for i, task in enumerate(sequence, 1):
                print(f"  {i}. {task}")
            
            # 询问是否执行
            choice = input("\n是否开始执行任务序列? (y/n): ").lower()
            
            if choice == 'y':
                print("\n开始执行任务...")
                
                def cli_callback(task_name, status, current, total):
                    print(f"[{current}/{total}] {task_name}: {status}")
                
                success = executor.execute_task_sequence(callback=cli_callback)
                
                if success:
                    print("\n✓ 所有任务执行完成！")
                else:
                    print("\n✗ 任务执行失败！")
            else:
                print("\n任务执行已取消")
            
            input("\n按回车键退出...")
            
        except Exception as e:
            self.logger.error(f"命令行模式执行失败: {e}")
            print(f"错误: {e}")
            input("按回车键退出...")
    
    def test_configuration(self, parent_root):
        """测试配置"""
        try:
            self.logger.info("开始测试配置")
            
            # 创建测试窗口
            test_window = tk.Toplevel(parent_root)
            test_window.title("配置测试")
            test_window.geometry("600x400")
            
            # 测试结果显示
            result_text = tk.Text(test_window, wrap=tk.WORD)
            result_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # 执行测试
            result_text.insert(tk.END, "开始配置测试...\n\n")
            test_window.update()
            
            # 测试资源管理器
            result_text.insert(tk.END, "1. 测试资源管理器...\n")
            test_window.update()
            
            try:
                resource_manager = MaaResourceManager()
                result_text.insert(tk.END, "   ✓ 资源管理器初始化成功\n")
                
                # 验证配置
                errors = resource_manager.validate_pipeline()
                if errors:
                    result_text.insert(tk.END, f"   ⚠ 发现配置问题: {len(errors)}个\n")
                    for error in errors[:3]:  # 只显示前3个错误
                        result_text.insert(tk.END, f"     - {error}\n")
                else:
                    result_text.insert(tk.END, "   ✓ 配置验证通过\n")
                
            except Exception as e:
                result_text.insert(tk.END, f"   ✗ 资源管理器测试失败: {e}\n")
            
            test_window.update()
            
            # 测试自定义动作
            result_text.insert(tk.END, "\n2. 测试自定义动作...\n")
            test_window.update()
            
            try:
                custom_actions = MaaCustomActionRegistry()
                
                # 测试读取邮箱文件
                test_config = {
                    "action": "ReadEmailFromFile",
                    "file_name": "邮箱.txt",
                    "line_number": 1
                }
                
                success = custom_actions.execute_action("ReadEmailFromFile", test_config, "resource")
                if success:
                    email = test_config.get('input_text', '无')
                    result_text.insert(tk.END, f"   ✓ 读取邮箱成功: {email}\n")
                else:
                    result_text.insert(tk.END, "   ✗ 读取邮箱失败\n")
                
            except Exception as e:
                result_text.insert(tk.END, f"   ✗ 自定义动作测试失败: {e}\n")
            
            test_window.update()
            
            # 测试任务执行器
            result_text.insert(tk.END, "\n3. 测试任务执行器...\n")
            test_window.update()
            
            try:
                executor = MaaEnhancedTaskExecutor(resource_manager, custom_actions)
                sequence = resource_manager.get_task_sequence()
                result_text.insert(tk.END, f"   ✓ 任务序列生成成功，共{len(sequence)}个任务\n")
                
            except Exception as e:
                result_text.insert(tk.END, f"   ✗ 任务执行器测试失败: {e}\n")
            
            result_text.insert(tk.END, "\n配置测试完成！\n")
            result_text.config(state=tk.DISABLED)
            
        except Exception as e:
            self.logger.error(f"配置测试失败: {e}")
            messagebox.showerror("错误", f"配置测试失败: {e}")


def main():
    """主函数"""
    try:
        launcher = MaaLauncher()
        launcher.show_launcher_ui()
    except Exception as e:
        print(f"启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
