# MaaFramework调试指南

## 使用maaDebugger调试步骤

### 1. 启动maaDebugger
```bash
# 如果已安装MaaFramework
maaDebugger

# 或者从MaaFramework安装目录启动
./MaaDebugger
```

### 2. 配置资源路径
1. 在maaDebugger界面中点击"设置"或"Settings"
2. 设置资源路径为: `C:\Users\<USER>\Desktop\demo\debug_resource`
3. 点击"加载资源"或"Load Resource"

### 3. 选择Pipeline文件
1. 在"Pipeline"选项卡中
2. 选择文件: `C:\Users\<USER>\Desktop\demo\debug_resource/pipeline/pipeline.json`
3. 查看任务列表是否正确加载

### 4. 连接设备
1. 确保ADB设备已连接
2. 在"Controller"选项卡中配置设备连接
3. 测试连接是否成功

### 5. 调试任务
1. 选择要调试的任务
2. 点击"运行"或"Run"按钮
3. 观察执行结果和日志

## 常见问题解决

### 资源加载失败
- 检查资源路径是否正确
- 确认pipeline.json格式正确
- 检查图片资源是否存在

### 设备连接失败
- 确认ADB设备已连接: `adb devices`
- 检查设备地址配置
- 尝试重启ADB服务

### 任务执行失败
- 检查图片模板是否匹配
- 调整识别参数
- 查看详细错误日志

## 文件说明

- `pipeline/pipeline.json`: 主要任务配置（已转换为标准格式）
- `pipeline/pipeline_original.json`: 原始配置备份
- `config/settings.json`: MaaFramework设置
- `interface.json`: 接口配置
- `image/`: 图片资源文件夹

## 注意事项

1. 此配置已将自定义动作转换为标准MaaFramework动作
2. 如需使用自定义动作，请使用Python启动器
3. 调试完成后可以将修改应用回原始配置
