2025-08-05 14:54:23,968 - maa_automation_system - INFO - 成功加载pipeline配置，包含17个任务
2025-08-05 14:54:23,969 - maa_automation_system - INFO - MaaResourceManager初始化完成
2025-08-05 14:54:26,720 - __main__ - INFO - 启动完整功能界面
2025-08-05 14:54:26,747 - maa_automation_system - INFO - 成功加载pipeline配置，包含17个任务
2025-08-05 14:54:26,747 - maa_automation_system - INFO - MaaResourceManager初始化完成
2025-08-05 14:54:26,747 - maa_automation_system - INFO - 生成任务序列，共17个任务
2025-08-05 14:54:29,521 - maa_automation_system - INFO - Pipeline验证通过
2025-08-05 14:54:37,231 - maa_automation_system - INFO - 生成任务序列，共17个任务
2025-08-05 14:54:49,221 - maa_automation_system - INFO - 生成任务序列，共17个任务
2025-08-05 14:54:49,221 - maa_automation_system - INFO - 开始执行任务序列: 启动应用伪装 -> 点击配置应用 -> 点击华为视频 -> 点击右上角 -> 点击清除应用数据 -> 点击保存 -> 点击停止 -> 启动华为视频 -> 点击同意 -> 点击取消 -> 点击我的 -> 点击未登录 -> 点击继续使用 -> 点击密码登录 -> 点击继续 -> 点击手机号 -> 输入邮件地址
2025-08-05 14:54:49,221 - maa_automation_system - INFO - 执行任务 [1/17]: 启动应用伪装
2025-08-05 14:54:49,221 - maa_automation_system - INFO - 执行动作: StartApp
2025-08-05 14:54:49,722 - maa_automation_system - INFO - 执行任务 [2/17]: 点击配置应用
2025-08-05 14:54:49,727 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:54:50,229 - maa_automation_system - INFO - 执行任务 [3/17]: 点击华为视频
2025-08-05 14:54:50,235 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:54:50,736 - maa_automation_system - INFO - 执行任务 [4/17]: 点击右上角
2025-08-05 14:54:50,743 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:54:51,243 - maa_automation_system - INFO - 执行任务 [5/17]: 点击清除应用数据
2025-08-05 14:54:51,249 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:54:51,758 - maa_automation_system - INFO - 执行任务 [6/17]: 点击保存
2025-08-05 14:54:51,763 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:54:52,265 - maa_automation_system - INFO - 执行任务 [7/17]: 点击停止
2025-08-05 14:54:52,271 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:54:52,773 - maa_automation_system - INFO - 执行任务 [8/17]: 启动华为视频
2025-08-05 14:54:52,779 - maa_automation_system - INFO - 执行动作: StartApp
2025-08-05 14:54:53,280 - maa_automation_system - INFO - 执行任务 [9/17]: 点击同意
2025-08-05 14:54:53,286 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:54:53,787 - maa_automation_system - INFO - 执行任务 [10/17]: 点击取消
2025-08-05 14:54:53,794 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:54:54,297 - maa_automation_system - INFO - 执行任务 [11/17]: 点击我的
2025-08-05 14:54:54,301 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:54:54,802 - maa_automation_system - INFO - 执行任务 [12/17]: 点击未登录
2025-08-05 14:54:54,808 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:54:55,309 - maa_automation_system - INFO - 执行任务 [13/17]: 点击继续使用
2025-08-05 14:54:55,315 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:54:55,816 - maa_automation_system - INFO - 执行任务 [14/17]: 点击密码登录
2025-08-05 14:54:55,816 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:54:56,317 - maa_automation_system - INFO - 执行任务 [15/17]: 点击继续
2025-08-05 14:54:56,324 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:54:56,824 - maa_automation_system - INFO - 执行任务 [16/17]: 点击手机号
2025-08-05 14:54:56,825 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:54:57,327 - maa_automation_system - INFO - 执行任务 [17/17]: 输入邮件地址
2025-08-05 14:54:57,333 - maa_automation_system - INFO - 执行动作: ReadEmailFromFile
2025-08-05 14:54:57,834 - maa_automation_system - INFO - 任务序列执行完成
2025-08-05 14:56:05,439 - maa_automation_system - INFO - 成功加载pipeline配置，包含17个任务
2025-08-05 14:56:05,440 - maa_automation_system - INFO - MaaResourceManager初始化完成
2025-08-05 14:56:07,548 - __main__ - INFO - 启动简洁设计界面
2025-08-05 14:56:07,571 - maa_custom_actions - INFO - 注册自定义动作: ReadEmailFromFile
2025-08-05 14:56:07,571 - maa_custom_actions - INFO - 注册自定义动作: ReadTextFromFile
2025-08-05 14:56:07,571 - maa_custom_actions - INFO - 注册自定义动作: RandomSelectFromFile
2025-08-05 14:56:07,571 - maa_custom_actions - INFO - 注册自定义动作: ConditionalInput
2025-08-05 14:56:07,571 - maa_custom_actions - INFO - 注册自定义动作: DelayAction
2025-08-05 14:56:07,595 - maa_automation_system - INFO - 成功加载pipeline配置，包含17个任务
2025-08-05 14:56:07,596 - maa_automation_system - INFO - MaaResourceManager初始化完成
2025-08-05 14:56:25,398 - maa_automation_system - INFO - 成功加载pipeline配置，包含17个任务
2025-08-05 14:56:25,399 - maa_automation_system - INFO - MaaResourceManager初始化完成
2025-08-05 14:56:27,285 - __main__ - INFO - 启动命令行模式
2025-08-05 14:56:27,286 - maa_automation_system - INFO - 成功加载pipeline配置，包含17个任务
2025-08-05 14:56:27,286 - maa_automation_system - INFO - MaaResourceManager初始化完成
2025-08-05 14:56:27,286 - maa_custom_actions - INFO - 注册自定义动作: ReadEmailFromFile
2025-08-05 14:56:27,286 - maa_custom_actions - INFO - 注册自定义动作: ReadTextFromFile
2025-08-05 14:56:27,286 - maa_custom_actions - INFO - 注册自定义动作: RandomSelectFromFile
2025-08-05 14:56:27,286 - maa_custom_actions - INFO - 注册自定义动作: ConditionalInput
2025-08-05 14:56:27,286 - maa_custom_actions - INFO - 注册自定义动作: DelayAction
2025-08-05 14:56:27,286 - maa_automation_system - INFO - 生成任务序列，共17个任务
2025-08-05 14:57:09,565 - maa_automation_system - INFO - 成功加载pipeline配置，包含17个任务
2025-08-05 14:57:09,566 - maa_automation_system - INFO - MaaResourceManager初始化完成
2025-08-05 14:57:11,411 - __main__ - INFO - 开始测试配置
2025-08-05 14:57:11,422 - maa_automation_system - INFO - 成功加载pipeline配置，包含17个任务
2025-08-05 14:57:11,422 - maa_automation_system - INFO - MaaResourceManager初始化完成
2025-08-05 14:57:11,422 - maa_automation_system - INFO - Pipeline验证通过
2025-08-05 14:57:11,470 - maa_custom_actions - INFO - 注册自定义动作: ReadEmailFromFile
2025-08-05 14:57:11,471 - maa_custom_actions - INFO - 注册自定义动作: ReadTextFromFile
2025-08-05 14:57:11,471 - maa_custom_actions - INFO - 注册自定义动作: RandomSelectFromFile
2025-08-05 14:57:11,471 - maa_custom_actions - INFO - 注册自定义动作: ConditionalInput
2025-08-05 14:57:11,471 - maa_custom_actions - INFO - 注册自定义动作: DelayAction
2025-08-05 14:57:11,472 - maa_custom_actions - INFO - 成功读取邮箱地址: <EMAIL>
2025-08-05 14:57:11,473 - maa_automation_system - INFO - 生成任务序列，共17个任务
2025-08-05 14:57:24,662 - __main__ - INFO - 启动简洁设计界面
2025-08-05 14:57:24,683 - maa_custom_actions - INFO - 注册自定义动作: ReadEmailFromFile
2025-08-05 14:57:24,683 - maa_custom_actions - INFO - 注册自定义动作: ReadTextFromFile
2025-08-05 14:57:24,683 - maa_custom_actions - INFO - 注册自定义动作: RandomSelectFromFile
2025-08-05 14:57:24,683 - maa_custom_actions - INFO - 注册自定义动作: ConditionalInput
2025-08-05 14:57:24,683 - maa_custom_actions - INFO - 注册自定义动作: DelayAction
2025-08-05 14:57:24,708 - maa_automation_system - INFO - 成功加载pipeline配置，包含17个任务
2025-08-05 14:57:24,708 - maa_automation_system - INFO - MaaResourceManager初始化完成
2025-08-05 14:57:33,424 - maa_automation_system - INFO - 成功加载pipeline配置，包含17个任务
2025-08-05 14:57:33,425 - maa_automation_system - INFO - MaaResourceManager初始化完成
2025-08-05 14:57:34,229 - __main__ - INFO - 启动完整功能界面
2025-08-05 14:57:34,256 - maa_automation_system - INFO - 成功加载pipeline配置，包含17个任务
2025-08-05 14:57:34,256 - maa_automation_system - INFO - MaaResourceManager初始化完成
2025-08-05 14:57:34,256 - maa_automation_system - INFO - 生成任务序列，共17个任务
2025-08-05 14:57:37,051 - maa_automation_system - INFO - Pipeline验证通过
2025-08-05 14:58:43,554 - maa_automation_system - INFO - Pipeline验证通过
2025-08-05 14:58:47,065 - maa_automation_system - INFO - 生成任务序列，共17个任务
2025-08-05 14:58:47,065 - maa_automation_system - INFO - 开始执行任务序列: 启动应用伪装 -> 点击配置应用 -> 点击华为视频 -> 点击右上角 -> 点击清除应用数据 -> 点击保存 -> 点击停止 -> 启动华为视频 -> 点击同意 -> 点击取消 -> 点击我的 -> 点击未登录 -> 点击继续使用 -> 点击密码登录 -> 点击继续 -> 点击手机号 -> 输入邮件地址
2025-08-05 14:58:47,065 - maa_automation_system - INFO - 执行任务 [1/17]: 启动应用伪装
2025-08-05 14:58:47,066 - maa_automation_system - INFO - 执行动作: StartApp
2025-08-05 14:58:47,566 - maa_automation_system - INFO - 执行任务 [2/17]: 点击配置应用
2025-08-05 14:58:47,566 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:58:48,067 - maa_automation_system - INFO - 执行任务 [3/17]: 点击华为视频
2025-08-05 14:58:48,067 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:58:48,568 - maa_automation_system - INFO - 执行任务 [4/17]: 点击右上角
2025-08-05 14:58:48,575 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:58:49,078 - maa_automation_system - INFO - 执行任务 [5/17]: 点击清除应用数据
2025-08-05 14:58:49,082 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:58:49,582 - maa_automation_system - INFO - 执行任务 [6/17]: 点击保存
2025-08-05 14:58:49,582 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:58:50,083 - maa_automation_system - INFO - 执行任务 [7/17]: 点击停止
2025-08-05 14:58:50,083 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:58:50,584 - maa_automation_system - INFO - 执行任务 [8/17]: 启动华为视频
2025-08-05 14:58:50,584 - maa_automation_system - INFO - 执行动作: StartApp
2025-08-05 14:58:51,085 - maa_automation_system - INFO - 执行任务 [9/17]: 点击同意
2025-08-05 14:58:51,091 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:58:51,592 - maa_automation_system - INFO - 执行任务 [10/17]: 点击取消
2025-08-05 14:58:51,592 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:58:52,093 - maa_automation_system - INFO - 执行任务 [11/17]: 点击我的
2025-08-05 14:58:52,093 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:58:52,594 - maa_automation_system - INFO - 执行任务 [12/17]: 点击未登录
2025-08-05 14:58:52,594 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:58:53,095 - maa_automation_system - INFO - 执行任务 [13/17]: 点击继续使用
2025-08-05 14:58:53,095 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:58:53,596 - maa_automation_system - INFO - 执行任务 [14/17]: 点击密码登录
2025-08-05 14:58:53,602 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:58:54,103 - maa_automation_system - INFO - 执行任务 [15/17]: 点击继续
2025-08-05 14:58:54,110 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:58:54,611 - maa_automation_system - INFO - 执行任务 [16/17]: 点击手机号
2025-08-05 14:58:54,611 - maa_automation_system - INFO - 执行动作: Click
2025-08-05 14:58:55,113 - maa_automation_system - INFO - 执行任务 [17/17]: 输入邮件地址
2025-08-05 14:58:55,113 - maa_automation_system - INFO - 执行动作: ReadEmailFromFile
2025-08-05 14:58:55,617 - maa_automation_system - INFO - 任务序列执行完成
2025-08-05 17:04:21,921 - maa_automation_system - INFO - 成功加载pipeline配置，包含17个任务
2025-08-05 17:04:21,921 - maa_automation_system - INFO - MaaResourceManager初始化完成
2025-08-05 17:04:44,690 - maa_automation_system - INFO - 成功加载pipeline配置，包含17个任务
2025-08-05 17:04:44,690 - maa_automation_system - INFO - MaaResourceManager初始化完成
