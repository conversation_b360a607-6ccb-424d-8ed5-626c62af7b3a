#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MaaFramework自定义动作模块
功能：扩展MaaFramework的动作功能，支持自定义业务逻辑
作者：AI Assistant
日期：2025-08-05
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
import random
import time

class MaaCustomActionRegistry:
    """MaaFramework自定义动作注册器"""
    
    def __init__(self):
        """初始化动作注册器"""
        self.actions = {}
        self.logger = logging.getLogger(__name__)
        
        # 注册内置自定义动作
        self._register_builtin_actions()
    
    def register_action(self, action_name: str, action_func):
        """
        注册自定义动作
        
        Args:
            action_name: 动作名称
            action_func: 动作执行函数
        """
        self.actions[action_name] = action_func
        self.logger.info(f"注册自定义动作: {action_name}")
    
    def execute_action(self, action_name: str, task_config: Dict[str, Any], 
                      resource_path: str = "resource") -> bool:
        """
        执行自定义动作
        
        Args:
            action_name: 动作名称
            task_config: 任务配置
            resource_path: 资源路径
            
        Returns:
            执行是否成功
        """
        if action_name in self.actions:
            try:
                return self.actions[action_name](task_config, resource_path)
            except Exception as e:
                self.logger.error(f"执行自定义动作失败 {action_name}: {e}")
                return False
        else:
            self.logger.warning(f"未找到自定义动作: {action_name}")
            return False
    
    def _register_builtin_actions(self):
        """注册内置自定义动作"""
        self.register_action("ReadEmailFromFile", self._read_email_from_file)
        self.register_action("ReadTextFromFile", self._read_text_from_file)
        self.register_action("RandomSelectFromFile", self._random_select_from_file)
        self.register_action("ConditionalInput", self._conditional_input)
        self.register_action("DelayAction", self._delay_action)
    
    def _read_email_from_file(self, task_config: Dict[str, Any], resource_path: str) -> bool:
        """
        从文件中读取邮箱地址（读取第一行）
        
        配置示例:
        {
            "action": "ReadEmailFromFile",
            "file_name": "邮箱.txt",
            "line_number": 1,  # 可选，默认为1
            "target_field": "input_text"  # 可选，默认为input_text
        }
        
        Args:
            task_config: 任务配置
            resource_path: 资源路径
            
        Returns:
            执行是否成功
        """
        try:
            file_name = task_config.get("file_name", "邮箱.txt")
            line_number = task_config.get("line_number", 1)
            target_field = task_config.get("target_field", "input_text")
            
            # 构建文件路径
            file_path = Path(resource_path) / "pipeline" / file_name
            
            if not file_path.exists():
                self.logger.error(f"文件不存在: {file_path}")
                return False
            
            # 读取指定行
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
                if line_number <= len(lines):
                    email = lines[line_number - 1].strip()
                    
                    # 验证邮箱格式（简单验证）
                    if '@' in email and '.' in email:
                        # 将读取的邮箱地址设置到配置中
                        task_config[target_field] = email
                        self.logger.info(f"成功读取邮箱地址: {email}")
                        return True
                    else:
                        self.logger.error(f"无效的邮箱格式: {email}")
                        return False
                else:
                    self.logger.error(f"行号超出范围: {line_number}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"读取邮箱文件失败: {e}")
            return False
    
    def _read_text_from_file(self, task_config: Dict[str, Any], resource_path: str) -> bool:
        """
        从文件中读取文本内容
        
        配置示例:
        {
            "action": "ReadTextFromFile",
            "file_name": "data.txt",
            "line_number": 1,
            "target_field": "input_text"
        }
        """
        try:
            file_name = task_config.get("file_name")
            line_number = task_config.get("line_number", 1)
            target_field = task_config.get("target_field", "input_text")
            
            if not file_name:
                self.logger.error("缺少file_name参数")
                return False
            
            file_path = Path(resource_path) / "pipeline" / file_name
            
            if not file_path.exists():
                self.logger.error(f"文件不存在: {file_path}")
                return False
            
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
                if line_number <= len(lines):
                    content = lines[line_number - 1].strip()
                    task_config[target_field] = content
                    self.logger.info(f"成功读取文本内容: {content}")
                    return True
                else:
                    self.logger.error(f"行号超出范围: {line_number}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"读取文本文件失败: {e}")
            return False
    
    def _random_select_from_file(self, task_config: Dict[str, Any], resource_path: str) -> bool:
        """
        从文件中随机选择一行内容
        
        配置示例:
        {
            "action": "RandomSelectFromFile",
            "file_name": "邮箱.txt",
            "target_field": "input_text"
        }
        """
        try:
            file_name = task_config.get("file_name")
            target_field = task_config.get("target_field", "input_text")
            
            if not file_name:
                self.logger.error("缺少file_name参数")
                return False
            
            file_path = Path(resource_path) / "pipeline" / file_name
            
            if not file_path.exists():
                self.logger.error(f"文件不存在: {file_path}")
                return False
            
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = [line.strip() for line in f.readlines() if line.strip()]
                
                if lines:
                    selected_content = random.choice(lines)
                    task_config[target_field] = selected_content
                    self.logger.info(f"随机选择内容: {selected_content}")
                    return True
                else:
                    self.logger.error("文件为空或没有有效内容")
                    return False
                    
        except Exception as e:
            self.logger.error(f"随机选择文件内容失败: {e}")
            return False
    
    def _conditional_input(self, task_config: Dict[str, Any], resource_path: str) -> bool:
        """
        条件输入动作
        
        配置示例:
        {
            "action": "ConditionalInput",
            "condition_field": "some_field",
            "condition_value": "expected_value",
            "true_action": {"action": "InputText", "input_text": "true_value"},
            "false_action": {"action": "InputText", "input_text": "false_value"}
        }
        """
        try:
            condition_field = task_config.get("condition_field")
            condition_value = task_config.get("condition_value")
            true_action = task_config.get("true_action")
            false_action = task_config.get("false_action")
            
            # 这里可以根据实际需求实现条件判断逻辑
            # 示例：简单的字符串比较
            current_value = task_config.get(condition_field)
            
            if current_value == condition_value:
                if true_action:
                    task_config.update(true_action)
                    self.logger.info("条件为真，执行true_action")
                    return True
            else:
                if false_action:
                    task_config.update(false_action)
                    self.logger.info("条件为假，执行false_action")
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"条件输入动作失败: {e}")
            return False
    
    def _delay_action(self, task_config: Dict[str, Any], resource_path: str) -> bool:
        """
        延迟动作
        
        配置示例:
        {
            "action": "DelayAction",
            "delay_seconds": 2.5
        }
        """
        try:
            delay_seconds = task_config.get("delay_seconds", 1.0)
            self.logger.info(f"延迟 {delay_seconds} 秒")
            time.sleep(delay_seconds)
            return True
            
        except Exception as e:
            self.logger.error(f"延迟动作失败: {e}")
            return False


class MaaEnhancedTaskExecutor:
    """增强的MaaFramework任务执行器，支持自定义动作"""
    
    def __init__(self, resource_manager, custom_action_registry: MaaCustomActionRegistry = None):
        """
        初始化增强任务执行器
        
        Args:
            resource_manager: 资源管理器实例
            custom_action_registry: 自定义动作注册器
        """
        self.resource_manager = resource_manager
        self.custom_actions = custom_action_registry or MaaCustomActionRegistry()
        self.logger = logging.getLogger(__name__)
        self.current_task = None
        self.task_status = {}
    
    def execute_task_sequence(self, start_task: str = None, callback=None) -> bool:
        """
        执行任务序列（支持自定义动作）
        
        Args:
            start_task: 起始任务名称
            callback: 状态回调函数
            
        Returns:
            执行是否成功
        """
        sequence = self.resource_manager.get_task_sequence(start_task)
        if not sequence:
            self.logger.error("无法获取任务序列")
            return False
        
        self.logger.info(f"开始执行任务序列: {' -> '.join(sequence)}")
        
        for i, task_name in enumerate(sequence):
            self.current_task = task_name
            self.logger.info(f"执行任务 [{i+1}/{len(sequence)}]: {task_name}")
            
            # 更新状态
            self.task_status[task_name] = "执行中"
            if callback:
                callback(task_name, "执行中", i+1, len(sequence))
            
            # 执行单个任务（包括自定义动作处理）
            success = self._execute_single_task(task_name)
            
            if success:
                self.task_status[task_name] = "完成"
                if callback:
                    callback(task_name, "完成", i+1, len(sequence))
            else:
                self.task_status[task_name] = "失败"
                if callback:
                    callback(task_name, "失败", i+1, len(sequence))
                self.logger.error(f"任务执行失败: {task_name}")
                return False
        
        self.logger.info("任务序列执行完成")
        return True
    
    def _execute_single_task(self, task_name: str) -> bool:
        """
        执行单个任务（支持自定义动作）
        
        Args:
            task_name: 任务名称
            
        Returns:
            执行是否成功
        """
        task_config = self.resource_manager.get_task_config(task_name)
        if not task_config:
            return False
        
        # 创建任务配置的副本，避免修改原始配置
        task_config_copy = task_config.copy()
        action = task_config_copy.get('action')
        
        self.logger.info(f"执行动作: {action}")
        
        # 检查是否为自定义动作
        if action in self.custom_actions.actions:
            # 执行自定义动作
            success = self.custom_actions.execute_action(
                action, task_config_copy, self.resource_manager.resource_path
            )
            
            if success:
                # 如果自定义动作修改了配置，可能需要继续执行标准动作
                standard_action = task_config_copy.get('standard_action')
                if standard_action:
                    self.logger.info(f"执行标准动作: {standard_action}")
                    # 这里调用MaaFramework的标准API
                    time.sleep(0.5)  # 模拟执行
                
                return True
            else:
                return False
        else:
            # 执行标准MaaFramework动作
            # 这里应该调用实际的MaaFramework API
            time.sleep(0.5)  # 模拟执行
            return True
    
    def get_current_status(self) -> Dict[str, Any]:
        """
        获取当前执行状态
        
        Returns:
            状态信息字典
        """
        return {
            'current_task': self.current_task,
            'task_status': self.task_status.copy()
        }


# 使用示例和测试
if __name__ == "__main__":
    # 创建自定义动作注册器
    custom_actions = MaaCustomActionRegistry()
    
    # 测试读取邮箱文件动作
    test_config = {
        "action": "ReadEmailFromFile",
        "file_name": "邮箱.txt",
        "line_number": 1
    }
    
    success = custom_actions.execute_action("ReadEmailFromFile", test_config, "resource")
    print(f"执行结果: {success}")
    print(f"读取的邮箱: {test_config.get('input_text', '无')}")
    
    # 测试随机选择动作
    test_config2 = {
        "action": "RandomSelectFromFile",
        "file_name": "邮箱.txt"
    }
    
    success2 = custom_actions.execute_action("RandomSelectFromFile", test_config2, "resource")
    print(f"随机选择结果: {success2}")
    print(f"随机选择的邮箱: {test_config2.get('input_text', '无')}")
    
    # 注册自定义动作示例
    def custom_email_validator(task_config, resource_path):
        """自定义邮箱验证动作"""
        email = task_config.get('input_text', '')
        if '@' in email and '.' in email.split('@')[1]:
            print(f"邮箱验证通过: {email}")
            return True
        else:
            print(f"邮箱验证失败: {email}")
            return False
    
    custom_actions.register_action("ValidateEmail", custom_email_validator)
    
    # 测试自定义验证动作
    test_config3 = {
        "action": "ValidateEmail",
        "input_text": "<EMAIL>"
    }
    
    success3 = custom_actions.execute_action("ValidateEmail", test_config3, "resource")
    print(f"邮箱验证结果: {success3}")
