# MaaFramework自动化任务系统

基于MaaFramework开发的完整自动化任务系统，支持资源整合、自定义UI、自定义动作和图片UI设计。

## 项目结构

```
demo/
├── resource/                    # 资源文件夹
│   ├── config/
│   │   └── settings.json       # 配置文件
│   ├── image/                  # 图片资源
│   │   ├── 应用伪装0.png
│   │   ├── 应用伪装1.png
│   │   ├── 应用伪装2.png
│   │   └── 取消.png
│   ├── model/                  # 模型文件
│   │   └── ocr/
│   └── pipeline/               # 任务流程配置
│       ├── pipeline.json       # 主要任务配置
│       └── 邮箱.txt            # 邮箱数据文件
├── maa_automation_system.py    # 核心资源管理和任务执行模块
├── maa_custom_actions.py       # 自定义动作模块
├── maa_ui.py                   # 完整功能UI界面
├── maa_ui_design.py           # 简洁设计UI界面
├── main.py                     # 主启动文件
└── README.md                   # 说明文档
```

## 功能特性

### 1. 资源文件与任务编排整合
- **智能配置解析**: 自动读取和解析pipeline.json配置文件
- **资源路径管理**: 统一管理图片、文本、配置等资源文件
- **任务序列生成**: 根据配置自动生成任务执行序列
- **配置验证**: 验证配置文件的完整性和正确性

### 2. 自定义Python前端UI
- **完整功能界面**: 基于tkinter的全功能管理界面
- **简洁设计界面**: 根据UI设计图实现的简洁界面
- **实时状态显示**: 显示任务执行状态、进度和日志
- **配置编辑**: 支持在线编辑和保存pipeline配置

### 3. 自定义动作模块
- **ReadEmailFromFile**: 从文件读取邮箱地址
- **ReadTextFromFile**: 从文件读取文本内容
- **RandomSelectFromFile**: 从文件随机选择内容
- **ConditionalInput**: 条件输入动作
- **DelayAction**: 延迟动作
- **动作注册机制**: 支持注册和扩展自定义动作

### 4. 多种启动模式
- **图形界面模式**: 两种不同风格的UI界面
- **命令行模式**: 支持命令行执行和调试
- **配置测试模式**: 验证系统配置和功能

## 快速开始

### 1. 环境要求
- Python 3.7+
- tkinter (通常随Python安装)
- MaaFramework (需要单独安装)

### 2. 启动系统
```bash
python main.py
```

### 3. 选择界面模式
启动后会显示启动器，可以选择：
- **完整功能界面**: 包含所有管理功能的界面
- **简洁设计界面**: 根据设计图实现的简洁界面
- **命令行模式**: 在终端中执行任务
- **测试配置**: 验证系统配置

## 详细使用说明

### 资源管理器使用

```python
from maa_automation_system import MaaResourceManager

# 创建资源管理器
resource_manager = MaaResourceManager("resource")

# 获取任务序列
sequence = resource_manager.get_task_sequence()
print("任务序列:", sequence)

# 获取任务配置
task_config = resource_manager.get_task_config("输入邮件地址")
print("任务配置:", task_config)

# 读取文本文件
email = resource_manager.read_text_file("邮箱.txt", 1)
print("第一个邮箱:", email)

# 验证配置
errors = resource_manager.validate_pipeline()
if errors:
    print("配置错误:", errors)
```

### 自定义动作使用

#### 1. 在pipeline.json中使用自定义动作

```json
{
    "输入邮件地址": {
        "recognition": "OCR",
        "expected": "邮件地址",
        "action": "ReadEmailFromFile",
        "file_name": "邮箱.txt",
        "line_number": 1,
        "standard_action": "InputText"
    }
}
```

#### 2. 注册新的自定义动作

```python
from maa_custom_actions import MaaCustomActionRegistry

# 创建动作注册器
custom_actions = MaaCustomActionRegistry()

# 定义自定义动作
def my_custom_action(task_config, resource_path):
    """自定义动作示例"""
    print("执行自定义动作")
    return True

# 注册动作
custom_actions.register_action("MyCustomAction", my_custom_action)

# 执行动作
task_config = {"action": "MyCustomAction"}
success = custom_actions.execute_action("MyCustomAction", task_config, "resource")
```

### UI界面使用

#### 完整功能界面
- 支持配置文件加载和编辑
- 实时显示任务执行状态
- 提供详细的日志信息
- 支持任务验证和调试

#### 简洁设计界面
- 基于提供的UI设计图实现
- 显示模拟器列表和邮箱信息
- 支持批量任务执行
- 实时进度显示

## 自定义动作开发指南

### 1. 动作函数签名
```python
def custom_action(task_config: Dict[str, Any], resource_path: str) -> bool:
    """
    自定义动作函数
    
    Args:
        task_config: 任务配置字典
        resource_path: 资源文件路径
        
    Returns:
        bool: 执行是否成功
    """
    # 实现自定义逻辑
    return True
```

### 2. 配置参数说明
- `file_name`: 要读取的文件名
- `line_number`: 行号（从1开始）
- `target_field`: 目标字段名（默认为input_text）
- `delay_seconds`: 延迟时间（秒）

### 3. 内置自定义动作

| 动作名称 | 功能描述 | 配置参数 |
|---------|---------|---------|
| ReadEmailFromFile | 从文件读取邮箱地址 | file_name, line_number |
| ReadTextFromFile | 从文件读取文本内容 | file_name, line_number |
| RandomSelectFromFile | 随机选择文件内容 | file_name |
| ConditionalInput | 条件输入 | condition_field, condition_value |
| DelayAction | 延迟执行 | delay_seconds |

## 配置文件说明

### pipeline.json结构
```json
{
    "任务名称": {
        "recognition": "识别方式",
        "template": "模板图片",
        "expected": "期望文本",
        "action": "动作类型",
        "input_text": "输入文本",
        "next": ["下一个任务"]
    }
}
```

### 支持的识别方式
- `TemplateMatch`: 模板匹配
- `OCR`: 文字识别

### 支持的动作类型
- `Click`: 点击
- `InputText`: 输入文本
- `StartApp`: 启动应用
- 自定义动作（如ReadEmailFromFile）

## 故障排除

### 常见问题

1. **配置文件加载失败**
   - 检查resource文件夹是否存在
   - 确认pipeline.json格式正确

2. **自定义动作执行失败**
   - 检查文件路径是否正确
   - 确认文件编码为UTF-8

3. **UI界面无法启动**
   - 确认Python版本支持tkinter
   - 检查模块导入是否正确

### 日志文件
- `maa_automation.log`: 系统运行日志
- `maa_launcher.log`: 启动器日志

## 扩展开发

### 添加新的UI组件
1. 在对应的UI文件中添加组件
2. 实现事件处理函数
3. 更新布局管理

### 开发新的自定义动作
1. 在`maa_custom_actions.py`中定义动作函数
2. 使用`register_action`注册动作
3. 在pipeline.json中使用新动作

### 集成MaaFramework API
1. 安装MaaFramework Python绑定
2. 在执行器中替换模拟执行代码
3. 添加实际的设备连接和操作逻辑

## 版本信息
- 版本: v1.0.0
- 开发者: AI Assistant
- 更新日期: 2025-08-05

## 许可证
本项目仅供学习和研究使用。
