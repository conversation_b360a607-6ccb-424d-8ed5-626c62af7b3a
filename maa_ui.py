#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MaaFramework自动化任务系统 - 自定义UI界面
功能：基于tkinter的前端界面，与pipeline.json配置文件交互
作者：AI Assistant
日期：2025-08-05
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import threading
import json
from pathlib import Path
from maa_automation_system import MaaResourceManager, MaaTaskExecutor

class MaaAutomationUI:
    """MaaFramework自动化任务系统UI界面"""
    
    def __init__(self):
        """初始化UI界面"""
        self.root = tk.Tk()
        self.root.title("MaaFramework自动化任务系统")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 初始化资源管理器和执行器
        self.resource_manager = None
        self.executor = None
        self.is_running = False
        
        # 创建UI组件
        self._create_widgets()
        self._setup_layout()
        
        # 尝试加载默认配置
        self._load_default_config()
    
    def _create_widgets(self):
        """创建UI组件"""
        # 主框架
        self.main_frame = ttk.Frame(self.root, padding="10")
        
        # 配置文件选择区域
        self.config_frame = ttk.LabelFrame(self.main_frame, text="配置文件", padding="5")
        
        self.config_path_var = tk.StringVar(value="resource")
        self.config_path_entry = ttk.Entry(self.config_frame, textvariable=self.config_path_var, width=50)
        self.browse_button = ttk.Button(self.config_frame, text="浏览", command=self._browse_config)
        self.load_button = ttk.Button(self.config_frame, text="加载配置", command=self._load_config)
        
        # 任务列表区域
        self.task_frame = ttk.LabelFrame(self.main_frame, text="任务列表", padding="5")
        
        # 创建Treeview来显示任务
        self.task_tree = ttk.Treeview(self.task_frame, columns=("action", "status"), show="tree headings", height=10)
        self.task_tree.heading("#0", text="任务名称")
        self.task_tree.heading("action", text="动作类型")
        self.task_tree.heading("status", text="状态")
        
        self.task_tree.column("#0", width=200)
        self.task_tree.column("action", width=150)
        self.task_tree.column("status", width=100)
        
        # 滚动条
        self.task_scrollbar = ttk.Scrollbar(self.task_frame, orient="vertical", command=self.task_tree.yview)
        self.task_tree.configure(yscrollcommand=self.task_scrollbar.set)
        
        # 控制按钮区域
        self.control_frame = ttk.LabelFrame(self.main_frame, text="任务控制", padding="5")
        
        self.start_button = ttk.Button(self.control_frame, text="开始执行", command=self._start_execution)
        self.stop_button = ttk.Button(self.control_frame, text="停止执行", command=self._stop_execution, state="disabled")
        self.validate_button = ttk.Button(self.control_frame, text="验证配置", command=self._validate_config)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.control_frame, variable=self.progress_var, maximum=100)
        
        # 状态显示区域
        self.status_frame = ttk.LabelFrame(self.main_frame, text="执行状态", padding="5")
        
        self.status_text = scrolledtext.ScrolledText(self.status_frame, height=8, width=70)
        
        # 配置编辑区域
        self.edit_frame = ttk.LabelFrame(self.main_frame, text="配置编辑", padding="5")
        
        self.edit_button = ttk.Button(self.edit_frame, text="编辑Pipeline", command=self._edit_pipeline)
        self.save_button = ttk.Button(self.edit_frame, text="保存配置", command=self._save_config)
        
        # 当前任务信息
        self.current_task_var = tk.StringVar(value="当前任务: 无")
        self.current_task_label = ttk.Label(self.control_frame, textvariable=self.current_task_var)
    
    def _setup_layout(self):
        """设置布局"""
        self.main_frame.grid(row=0, column=0, sticky="nsew")
        
        # 配置文件区域
        self.config_frame.grid(row=0, column=0, columnspan=2, sticky="ew", pady=(0, 10))
        self.config_path_entry.grid(row=0, column=0, padx=(0, 5))
        self.browse_button.grid(row=0, column=1, padx=(0, 5))
        self.load_button.grid(row=0, column=2)
        
        # 任务列表区域
        self.task_frame.grid(row=1, column=0, sticky="nsew", padx=(0, 10))
        self.task_tree.grid(row=0, column=0, sticky="nsew")
        self.task_scrollbar.grid(row=0, column=1, sticky="ns")
        
        # 控制按钮和状态区域
        control_status_frame = ttk.Frame(self.main_frame)
        control_status_frame.grid(row=1, column=1, sticky="nsew")
        
        self.control_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        self.current_task_label.grid(row=0, column=0, columnspan=3, pady=(0, 5))
        self.start_button.grid(row=1, column=0, padx=(0, 5))
        self.stop_button.grid(row=1, column=1, padx=(0, 5))
        self.validate_button.grid(row=1, column=2)
        self.progress_bar.grid(row=2, column=0, columnspan=3, sticky="ew", pady=(10, 0))
        
        self.status_frame.grid(row=1, column=0, sticky="nsew")
        self.status_text.grid(row=0, column=0, sticky="nsew")
        
        # 配置编辑区域
        self.edit_frame.grid(row=2, column=0, columnspan=2, sticky="ew", pady=(10, 0))
        self.edit_button.grid(row=0, column=0, padx=(0, 5))
        self.save_button.grid(row=0, column=1)
        
        # 配置权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        self.main_frame.columnconfigure(0, weight=2)
        self.main_frame.columnconfigure(1, weight=1)
        self.main_frame.rowconfigure(1, weight=1)
        
        self.task_frame.columnconfigure(0, weight=1)
        self.task_frame.rowconfigure(0, weight=1)
        
        control_status_frame.columnconfigure(0, weight=1)
        control_status_frame.rowconfigure(1, weight=1)
        
        self.status_frame.columnconfigure(0, weight=1)
        self.status_frame.rowconfigure(0, weight=1)
    
    def _browse_config(self):
        """浏览配置文件夹"""
        folder = filedialog.askdirectory(title="选择资源文件夹")
        if folder:
            self.config_path_var.set(folder)
    
    def _load_default_config(self):
        """加载默认配置"""
        try:
            self._load_config()
        except Exception as e:
            self._log_message(f"加载默认配置失败: {e}")
    
    def _load_config(self):
        """加载配置文件"""
        try:
            config_path = self.config_path_var.get()
            self.resource_manager = MaaResourceManager(config_path)
            self.executor = MaaTaskExecutor(self.resource_manager)
            
            self._update_task_list()
            self._log_message("配置加载成功")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载配置失败: {e}")
            self._log_message(f"配置加载失败: {e}")
    
    def _update_task_list(self):
        """更新任务列表显示"""
        # 清空现有项目
        for item in self.task_tree.get_children():
            self.task_tree.delete(item)
        
        if not self.resource_manager:
            return
        
        # 获取任务序列
        sequence = self.resource_manager.get_task_sequence()
        
        for i, task_name in enumerate(sequence):
            task_config = self.resource_manager.get_task_config(task_name)
            action = task_config.get('action', '未知') if task_config else '未知'
            status = "待执行"
            
            self.task_tree.insert("", "end", text=task_name, values=(action, status))
    
    def _start_execution(self):
        """开始执行任务"""
        if not self.executor:
            messagebox.showwarning("警告", "请先加载配置文件")
            return
        
        if self.is_running:
            messagebox.showwarning("警告", "任务正在执行中")
            return
        
        self.is_running = True
        self.start_button.config(state="disabled")
        self.stop_button.config(state="normal")
        
        # 在新线程中执行任务
        self.execution_thread = threading.Thread(target=self._execute_tasks)
        self.execution_thread.daemon = True
        self.execution_thread.start()
    
    def _stop_execution(self):
        """停止执行任务"""
        self.is_running = False
        self.start_button.config(state="normal")
        self.stop_button.config(state="disabled")
        self._log_message("任务执行已停止")
    
    def _execute_tasks(self):
        """执行任务（在后台线程中运行）"""
        try:
            def status_callback(task_name, status, current, total):
                if not self.is_running:
                    return
                
                # 更新UI（需要在主线程中执行）
                self.root.after(0, self._update_task_status, task_name, status, current, total)
            
            success = self.executor.execute_task_sequence(callback=status_callback)
            
            # 执行完成后更新UI
            self.root.after(0, self._execution_completed, success)
            
        except Exception as e:
            self.root.after(0, self._execution_error, str(e))
    
    def _update_task_status(self, task_name, status, current, total):
        """更新任务状态显示"""
        # 更新进度条
        progress = (current / total) * 100
        self.progress_var.set(progress)
        
        # 更新当前任务显示
        self.current_task_var.set(f"当前任务: {task_name}")
        
        # 更新任务列表中的状态
        for item in self.task_tree.get_children():
            if self.task_tree.item(item, "text") == task_name:
                values = list(self.task_tree.item(item, "values"))
                values[1] = status
                self.task_tree.item(item, values=values)
                break
        
        # 记录日志
        self._log_message(f"[{current}/{total}] {task_name}: {status}")
    
    def _execution_completed(self, success):
        """任务执行完成"""
        self.is_running = False
        self.start_button.config(state="normal")
        self.stop_button.config(state="disabled")
        self.current_task_var.set("当前任务: 无")
        
        if success:
            self.progress_var.set(100)
            self._log_message("所有任务执行完成！")
            messagebox.showinfo("完成", "所有任务执行完成！")
        else:
            self._log_message("任务执行失败！")
            messagebox.showerror("错误", "任务执行失败！")
    
    def _execution_error(self, error_msg):
        """任务执行出错"""
        self.is_running = False
        self.start_button.config(state="normal")
        self.stop_button.config(state="disabled")
        self.current_task_var.set("当前任务: 无")
        
        self._log_message(f"执行出错: {error_msg}")
        messagebox.showerror("错误", f"执行出错: {error_msg}")
    
    def _validate_config(self):
        """验证配置文件"""
        if not self.resource_manager:
            messagebox.showwarning("警告", "请先加载配置文件")
            return
        
        errors = self.resource_manager.validate_pipeline()
        if errors:
            error_msg = "\n".join(errors)
            messagebox.showwarning("配置验证", f"发现以下问题:\n{error_msg}")
            self._log_message(f"配置验证失败，发现{len(errors)}个问题")
        else:
            messagebox.showinfo("配置验证", "配置验证通过！")
            self._log_message("配置验证通过")
    
    def _edit_pipeline(self):
        """编辑Pipeline配置"""
        if not self.resource_manager:
            messagebox.showwarning("警告", "请先加载配置文件")
            return
        
        # 创建编辑窗口
        edit_window = tk.Toplevel(self.root)
        edit_window.title("编辑Pipeline配置")
        edit_window.geometry("600x400")
        
        # 文本编辑器
        text_editor = scrolledtext.ScrolledText(edit_window, wrap=tk.WORD)
        text_editor.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 加载当前配置
        try:
            config_json = json.dumps(self.resource_manager.pipeline_config, 
                                   ensure_ascii=False, indent=4)
            text_editor.insert(tk.END, config_json)
        except Exception as e:
            messagebox.showerror("错误", f"加载配置失败: {e}")
            edit_window.destroy()
            return
        
        # 保存按钮
        def save_changes():
            try:
                new_config = json.loads(text_editor.get(1.0, tk.END))
                self.resource_manager.pipeline_config = new_config
                self._update_task_list()
                self._log_message("Pipeline配置已更新")
                edit_window.destroy()
            except json.JSONDecodeError as e:
                messagebox.showerror("错误", f"JSON格式错误: {e}")
        
        save_btn = ttk.Button(edit_window, text="保存", command=save_changes)
        save_btn.pack(pady=5)
    
    def _save_config(self):
        """保存配置到文件"""
        if not self.resource_manager:
            messagebox.showwarning("警告", "请先加载配置文件")
            return
        
        try:
            with open(self.resource_manager.pipeline_path, 'w', encoding='utf-8') as f:
                json.dump(self.resource_manager.pipeline_config, f, 
                         ensure_ascii=False, indent=4)
            
            self._log_message("配置已保存到文件")
            messagebox.showinfo("保存", "配置已保存到文件")
            
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def _log_message(self, message):
        """记录日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.status_text.insert(tk.END, log_entry)
        self.status_text.see(tk.END)
    
    def run(self):
        """运行UI界面"""
        self.root.mainloop()


# 使用示例
if __name__ == "__main__":
    app = MaaAutomationUI()
    app.run()
