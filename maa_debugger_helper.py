#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MaaFramework调试助手
功能：为maaDebugger创建兼容的配置文件和资源结构
作者：AI Assistant
日期：2025-08-05
"""

import json
import os
import shutil
from pathlib import Path
from typing import Dict, Any

class MaaDebuggerHelper:
    """MaaFramework调试助手"""
    
    def __init__(self, resource_path: str = "resource"):
        """
        初始化调试助手
        
        Args:
            resource_path: 资源文件夹路径
        """
        self.resource_path = Path(resource_path)
        self.debug_resource_path = Path("debug_resource")
        
    def create_debugger_compatible_config(self):
        """创建maaDebugger兼容的配置文件"""
        print("正在创建maaDebugger兼容的配置...")
        
        # 创建调试资源目录
        if self.debug_resource_path.exists():
            shutil.rmtree(self.debug_resource_path)
        self.debug_resource_path.mkdir(exist_ok=True)
        
        # 复制必要的目录结构
        self._copy_resource_structure()
        
        # 创建兼容的pipeline.json
        self._create_compatible_pipeline()
        
        # 创建正确的settings.json
        self._create_settings_config()
        
        # 创建interface.json（MaaFramework需要）
        self._create_interface_config()
        
        print(f"调试配置已创建在: {self.debug_resource_path.absolute()}")
        print("\n使用方法:")
        print(f"1. 在maaDebugger中设置资源路径为: {self.debug_resource_path.absolute()}")
        print("2. 选择pipeline.json文件进行调试")
        
    def _copy_resource_structure(self):
        """复制资源文件结构"""
        # 复制图片资源
        src_image = self.resource_path / "image"
        dst_image = self.debug_resource_path / "image"
        if src_image.exists():
            shutil.copytree(src_image, dst_image)
            print(f"✓ 复制图片资源: {len(list(dst_image.glob('*.png')))} 个文件")
        
        # 复制模型资源
        src_model = self.resource_path / "model"
        dst_model = self.debug_resource_path / "model"
        if src_model.exists():
            shutil.copytree(src_model, dst_model)
            print("✓ 复制模型资源")
        
        # 创建必要的目录
        (self.debug_resource_path / "pipeline").mkdir(exist_ok=True)
        (self.debug_resource_path / "config").mkdir(exist_ok=True)
        
    def _create_compatible_pipeline(self):
        """创建maaDebugger兼容的pipeline.json"""
        # 读取原始配置
        original_pipeline_path = self.resource_path / "pipeline" / "pipeline.json"
        
        if not original_pipeline_path.exists():
            print("⚠ 原始pipeline.json不存在")
            return
        
        with open(original_pipeline_path, 'r', encoding='utf-8') as f:
            original_config = json.load(f)
        
        # 转换为maaDebugger兼容的配置
        compatible_config = {}
        
        for task_name, task_config in original_config.items():
            new_config = task_config.copy()
            
            # 处理自定义动作
            if new_config.get('action') == 'ReadEmailFromFile':
                # 将自定义动作转换为标准InputText动作
                new_config['action'] = 'InputText'
                
                # 读取邮箱文件内容
                email_file = self.resource_path / "pipeline" / "邮箱.txt"
                if email_file.exists():
                    with open(email_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        line_number = new_config.get('line_number', 1)
                        if line_number <= len(lines):
                            email = lines[line_number - 1].strip()
                            new_config['input_text'] = email
                            print(f"✓ 设置邮箱地址: {email}")
                
                # 移除自定义字段
                new_config.pop('file_name', None)
                new_config.pop('line_number', None)
                new_config.pop('standard_action', None)
            
            compatible_config[task_name] = new_config
        
        # 保存兼容的配置
        debug_pipeline_path = self.debug_resource_path / "pipeline" / "pipeline.json"
        with open(debug_pipeline_path, 'w', encoding='utf-8') as f:
            json.dump(compatible_config, f, ensure_ascii=False, indent=4)
        
        print(f"✓ 创建兼容的pipeline.json: {len(compatible_config)} 个任务")
        
        # 同时创建一个原始配置的备份
        backup_path = self.debug_resource_path / "pipeline" / "pipeline_original.json"
        shutil.copy2(original_pipeline_path, backup_path)
        print("✓ 创建原始配置备份")
    
    def _create_settings_config(self):
        """创建settings.json配置"""
        settings = {
            "controller": {
                "name": "Adb",
                "adb_path": "adb",
                "address": "127.0.0.1:5555",
                "config": {
                    "extras": {
                        "maatouch": {
                            "package": "com.shxyke.MaaTouch.App"
                        }
                    }
                }
            },
            "resource": [
                {
                    "name": "Official",
                    "path": "{PROJECT_DIR}"
                }
            ],
            "task": [
                {
                    "name": "启动应用伪装",
                    "entry": "启动应用伪装"
                }
            ],
            "version": "v4.28.0",
            "message": "MaaFramework调试配置"
        }
        
        settings_path = self.debug_resource_path / "config" / "settings.json"
        with open(settings_path, 'w', encoding='utf-8') as f:
            json.dump(settings, f, ensure_ascii=False, indent=4)
        
        print("✓ 创建settings.json配置")
    
    def _create_interface_config(self):
        """创建interface.json配置（MaaFramework接口配置）"""
        interface = {
            "resource": [
                {
                    "name": "PipelineResource",
                    "path": "pipeline"
                },
                {
                    "name": "ImageResource", 
                    "path": "image"
                }
            ],
            "task": [
                {
                    "name": "启动应用伪装",
                    "entry": "启动应用伪装",
                    "pipeline_override": {}
                }
            ]
        }
        
        interface_path = self.debug_resource_path / "interface.json"
        with open(interface_path, 'w', encoding='utf-8') as f:
            json.dump(interface, f, ensure_ascii=False, indent=4)
        
        print("✓ 创建interface.json配置")
    
    def validate_debugger_config(self):
        """验证调试配置"""
        print("\n验证调试配置...")
        
        errors = []
        warnings = []
        
        # 检查必要文件
        required_files = [
            "pipeline/pipeline.json",
            "config/settings.json", 
            "interface.json"
        ]
        
        for file_path in required_files:
            full_path = self.debug_resource_path / file_path
            if not full_path.exists():
                errors.append(f"缺少必要文件: {file_path}")
            else:
                print(f"✓ {file_path}")
        
        # 检查图片资源
        image_dir = self.debug_resource_path / "image"
        if image_dir.exists():
            png_files = list(image_dir.glob("*.png"))
            print(f"✓ 图片资源: {len(png_files)} 个文件")
            
            # 检查pipeline中引用的图片是否存在
            pipeline_path = self.debug_resource_path / "pipeline" / "pipeline.json"
            if pipeline_path.exists():
                with open(pipeline_path, 'r', encoding='utf-8') as f:
                    pipeline_config = json.load(f)
                
                for task_name, task_config in pipeline_config.items():
                    template = task_config.get('template')
                    if template:
                        template_path = image_dir / template
                        if not template_path.exists():
                            warnings.append(f"任务 '{task_name}' 引用的图片不存在: {template}")
                        else:
                            print(f"  ✓ {template}")
        else:
            warnings.append("图片资源目录不存在")
        
        # 输出结果
        if errors:
            print(f"\n❌ 发现 {len(errors)} 个错误:")
            for error in errors:
                print(f"  - {error}")
        
        if warnings:
            print(f"\n⚠ 发现 {len(warnings)} 个警告:")
            for warning in warnings:
                print(f"  - {warning}")
        
        if not errors and not warnings:
            print("\n✅ 配置验证通过！")
        
        return len(errors) == 0
    
    def create_debugger_guide(self):
        """创建调试指南"""
        guide_content = """# MaaFramework调试指南

## 使用maaDebugger调试步骤

### 1. 启动maaDebugger
```bash
# 如果已安装MaaFramework
maaDebugger

# 或者从MaaFramework安装目录启动
./MaaDebugger
```

### 2. 配置资源路径
1. 在maaDebugger界面中点击"设置"或"Settings"
2. 设置资源路径为: `{debug_path}`
3. 点击"加载资源"或"Load Resource"

### 3. 选择Pipeline文件
1. 在"Pipeline"选项卡中
2. 选择文件: `{debug_path}/pipeline/pipeline.json`
3. 查看任务列表是否正确加载

### 4. 连接设备
1. 确保ADB设备已连接
2. 在"Controller"选项卡中配置设备连接
3. 测试连接是否成功

### 5. 调试任务
1. 选择要调试的任务
2. 点击"运行"或"Run"按钮
3. 观察执行结果和日志

## 常见问题解决

### 资源加载失败
- 检查资源路径是否正确
- 确认pipeline.json格式正确
- 检查图片资源是否存在

### 设备连接失败
- 确认ADB设备已连接: `adb devices`
- 检查设备地址配置
- 尝试重启ADB服务

### 任务执行失败
- 检查图片模板是否匹配
- 调整识别参数
- 查看详细错误日志

## 文件说明

- `pipeline/pipeline.json`: 主要任务配置（已转换为标准格式）
- `pipeline/pipeline_original.json`: 原始配置备份
- `config/settings.json`: MaaFramework设置
- `interface.json`: 接口配置
- `image/`: 图片资源文件夹

## 注意事项

1. 此配置已将自定义动作转换为标准MaaFramework动作
2. 如需使用自定义动作，请使用Python启动器
3. 调试完成后可以将修改应用回原始配置
"""
        
        guide_path = self.debug_resource_path / "调试指南.md"
        with open(guide_path, 'w', encoding='utf-8') as f:
            f.write(guide_content.format(
                debug_path=self.debug_resource_path.absolute()
            ))
        
        print(f"✓ 创建调试指南: {guide_path}")


def main():
    """主函数"""
    print("MaaFramework调试助手")
    print("=" * 50)
    
    helper = MaaDebuggerHelper()
    
    try:
        # 创建调试配置
        helper.create_debugger_compatible_config()
        
        # 验证配置
        if helper.validate_debugger_config():
            print("\n🎉 调试配置创建成功！")
        else:
            print("\n⚠ 配置创建完成，但存在一些问题，请检查上述错误")
        
        # 创建调试指南
        helper.create_debugger_guide()
        
        print(f"\n📁 调试资源目录: {helper.debug_resource_path.absolute()}")
        print("\n下一步:")
        print("1. 启动maaDebugger")
        print(f"2. 设置资源路径为: {helper.debug_resource_path.absolute()}")
        print("3. 加载pipeline.json文件")
        print("4. 开始调试")
        
    except Exception as e:
        print(f"\n❌ 创建调试配置失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
