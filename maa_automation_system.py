#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MaaFramework自动化任务系统 - 核心模块
功能：资源文件与任务编排整合
作者：AI Assistant
日期：2025-08-05
"""

import json
import os
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
import time

class MaaResourceManager:
    """MaaFramework资源管理器"""
    
    def __init__(self, resource_path: str = "resource"):
        """
        初始化资源管理器
        
        Args:
            resource_path: 资源文件夹路径
        """
        self.resource_path = Path(resource_path)
        self.pipeline_path = self.resource_path / "pipeline" / "pipeline.json"
        self.image_path = self.resource_path / "image"
        self.config_path = self.resource_path / "config"
        self.model_path = self.resource_path / "model"
        
        # 初始化日志
        self._setup_logging()
        
        # 加载配置
        self.pipeline_config = self._load_pipeline_config()
        self.settings = self._load_settings()
        
        self.logger.info("MaaResourceManager初始化完成")
    
    def _setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('maa_automation.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def _load_pipeline_config(self) -> Dict[str, Any]:
        """
        加载pipeline.json配置文件
        
        Returns:
            pipeline配置字典
        """
        try:
            if self.pipeline_path.exists():
                with open(self.pipeline_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                self.logger.info(f"成功加载pipeline配置，包含{len(config)}个任务")
                return config
            else:
                self.logger.warning(f"Pipeline配置文件不存在: {self.pipeline_path}")
                return {}
        except Exception as e:
            self.logger.error(f"加载pipeline配置失败: {e}")
            return {}
    
    def _load_settings(self) -> Dict[str, Any]:
        """
        加载settings.json配置文件
        
        Returns:
            设置配置字典
        """
        settings_file = self.config_path / "settings.json"
        try:
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if content:
                        return json.loads(content)
                    else:
                        return {}
            else:
                return {}
        except Exception as e:
            self.logger.error(f"加载设置配置失败: {e}")
            return {}
    
    def get_task_sequence(self, start_task: str = None) -> List[str]:
        """
        获取任务执行序列
        
        Args:
            start_task: 起始任务名称，如果为None则从第一个任务开始
            
        Returns:
            任务执行序列列表
        """
        if not self.pipeline_config:
            return []
        
        if start_task is None:
            # 找到第一个任务（没有被其他任务引用的任务）
            all_tasks = set(self.pipeline_config.keys())
            referenced_tasks = set()
            
            for task_config in self.pipeline_config.values():
                if 'next' in task_config:
                    referenced_tasks.update(task_config['next'])
            
            start_tasks = all_tasks - referenced_tasks
            if start_tasks:
                start_task = list(start_tasks)[0]
            else:
                start_task = list(all_tasks)[0]
        
        sequence = []
        current_task = start_task
        visited = set()
        
        while current_task and current_task not in visited:
            if current_task in self.pipeline_config:
                sequence.append(current_task)
                visited.add(current_task)
                
                # 获取下一个任务
                task_config = self.pipeline_config[current_task]
                if 'next' in task_config and task_config['next']:
                    current_task = task_config['next'][0]  # 取第一个下一步任务
                else:
                    break
            else:
                break
        
        self.logger.info(f"生成任务序列，共{len(sequence)}个任务")
        return sequence
    
    def get_task_config(self, task_name: str) -> Optional[Dict[str, Any]]:
        """
        获取指定任务的配置
        
        Args:
            task_name: 任务名称
            
        Returns:
            任务配置字典或None
        """
        return self.pipeline_config.get(task_name)
    
    def get_image_path(self, image_name: str) -> Optional[Path]:
        """
        获取图片资源的完整路径
        
        Args:
            image_name: 图片文件名
            
        Returns:
            图片文件路径或None
        """
        image_file = self.image_path / image_name
        if image_file.exists():
            return image_file
        else:
            self.logger.warning(f"图片文件不存在: {image_file}")
            return None
    
    def read_text_file(self, file_name: str, line_number: int = 1) -> Optional[str]:
        """
        读取文本文件的指定行
        
        Args:
            file_name: 文件名
            line_number: 行号（从1开始）
            
        Returns:
            文本内容或None
        """
        file_path = self.resource_path / "pipeline" / file_name
        try:
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    if 1 <= line_number <= len(lines):
                        content = lines[line_number - 1].strip()
                        self.logger.info(f"读取文件 {file_name} 第{line_number}行: {content}")
                        return content
                    else:
                        self.logger.warning(f"行号超出范围: {line_number}")
                        return None
            else:
                self.logger.warning(f"文件不存在: {file_path}")
                return None
        except Exception as e:
            self.logger.error(f"读取文件失败: {e}")
            return None
    
    def validate_pipeline(self) -> List[str]:
        """
        验证pipeline配置的完整性
        
        Returns:
            错误信息列表
        """
        errors = []
        
        for task_name, task_config in self.pipeline_config.items():
            # 检查必要字段
            if 'action' not in task_config:
                errors.append(f"任务 '{task_name}' 缺少 'action' 字段")
            
            # 检查图片资源
            if task_config.get('recognition') == 'TemplateMatch':
                template = task_config.get('template')
                if template and not self.get_image_path(template):
                    errors.append(f"任务 '{task_name}' 的模板图片不存在: {template}")
            
            # 检查下一步任务
            if 'next' in task_config:
                for next_task in task_config['next']:
                    if next_task not in self.pipeline_config:
                        errors.append(f"任务 '{task_name}' 的下一步任务不存在: {next_task}")
        
        if errors:
            self.logger.warning(f"Pipeline验证发现{len(errors)}个问题")
        else:
            self.logger.info("Pipeline验证通过")
        
        return errors


class MaaTaskExecutor:
    """MaaFramework任务执行器"""
    
    def __init__(self, resource_manager: MaaResourceManager):
        """
        初始化任务执行器
        
        Args:
            resource_manager: 资源管理器实例
        """
        self.resource_manager = resource_manager
        self.logger = logging.getLogger(__name__)
        self.current_task = None
        self.task_status = {}
    
    def execute_task_sequence(self, start_task: str = None, callback=None) -> bool:
        """
        执行任务序列
        
        Args:
            start_task: 起始任务名称
            callback: 状态回调函数
            
        Returns:
            执行是否成功
        """
        sequence = self.resource_manager.get_task_sequence(start_task)
        if not sequence:
            self.logger.error("无法获取任务序列")
            return False
        
        self.logger.info(f"开始执行任务序列: {' -> '.join(sequence)}")
        
        for i, task_name in enumerate(sequence):
            self.current_task = task_name
            self.logger.info(f"执行任务 [{i+1}/{len(sequence)}]: {task_name}")
            
            # 更新状态
            self.task_status[task_name] = "执行中"
            if callback:
                callback(task_name, "执行中", i+1, len(sequence))
            
            # 模拟任务执行（实际项目中这里会调用MaaFramework的API）
            success = self._execute_single_task(task_name)
            
            if success:
                self.task_status[task_name] = "完成"
                if callback:
                    callback(task_name, "完成", i+1, len(sequence))
            else:
                self.task_status[task_name] = "失败"
                if callback:
                    callback(task_name, "失败", i+1, len(sequence))
                self.logger.error(f"任务执行失败: {task_name}")
                return False
        
        self.logger.info("任务序列执行完成")
        return True
    
    def _execute_single_task(self, task_name: str) -> bool:
        """
        执行单个任务
        
        Args:
            task_name: 任务名称
            
        Returns:
            执行是否成功
        """
        task_config = self.resource_manager.get_task_config(task_name)
        if not task_config:
            return False
        
        action = task_config.get('action')
        self.logger.info(f"执行动作: {action}")
        
        # 模拟执行时间
        time.sleep(0.5)
        
        # 这里应该调用实际的MaaFramework API
        # 目前只是模拟执行
        return True
    
    def get_current_status(self) -> Dict[str, Any]:
        """
        获取当前执行状态
        
        Returns:
            状态信息字典
        """
        return {
            'current_task': self.current_task,
            'task_status': self.task_status.copy()
        }


# 使用示例
if __name__ == "__main__":
    # 创建资源管理器
    resource_manager = MaaResourceManager()
    
    # 验证配置
    errors = resource_manager.validate_pipeline()
    if errors:
        print("配置验证错误:")
        for error in errors:
            print(f"  - {error}")
    
    # 获取任务序列
    sequence = resource_manager.get_task_sequence()
    print(f"任务序列: {' -> '.join(sequence)}")
    
    # 创建任务执行器
    executor = MaaTaskExecutor(resource_manager)
    
    # 定义状态回调函数
    def status_callback(task_name, status, current, total):
        print(f"[{current}/{total}] {task_name}: {status}")
    
    # 执行任务序列
    success = executor.execute_task_sequence(callback=status_callback)
    print(f"执行结果: {'成功' if success else '失败'}")
